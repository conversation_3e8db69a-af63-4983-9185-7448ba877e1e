package com.onre.rewardsapi.domain.jobexecution;

import com.onre.rewardsapi.domain.jobexecution.repository.JobExecutionRepository;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.time.LocalDate;

/**
 * Domain service for creating job execution records.
 */
@Service
public class JobExecutionCreateService {

    private final JobExecutionRepository jobExecutionRepository;

    public JobExecutionCreateService(JobExecutionRepository jobExecutionRepository) {
        this.jobExecutionRepository = jobExecutionRepository;
    }

    /**
     * Create a new job execution record
     */
    @Transactional
    public JobExecution create(
            JobType jobType,
            LocalDate executionDate,
            Instant executionStartTime
    ) {
        JobExecution jobExecution = JobExecution.create(
                jobType,
                executionDate,
                executionStartTime
        );

        return jobExecutionRepository.save(jobExecution);
    }
}
