package com.onre.rewardsapi.domain.jobexecution;

import com.onre.rewardsapi.domain.CreatableEntity;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.time.Instant;
import java.time.LocalDate;

/**
 * Entity representing the execution of a job for a specific date.
 * Used to track job dependencies and ensure proper execution order.
 */
@Entity
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor(access = lombok.AccessLevel.PRIVATE)
public class JobExecution extends CreatableEntity {

    /**
     * The type of job that was executed
     */
    @Enumerated(EnumType.STRING)
    private JobType jobType;

    /**
     * The date for which the job was executed (in UTC)
     */
    private LocalDate executionDate;

    /**
     * The timestamp when the job execution started
     */
    private Instant executionStartTime;

    /**
     * The timestamp when the job execution completed
     */
    private Instant executionEndTime;

    /**
     * The status of the job execution
     */
    @Enumerated(EnumType.STRING)
    private JobExecutionStatus status;

    /**
     * Optional error message if the job failed
     */
    private String errorMessage;

    /**
     * Factory method to create a new job execution record
     */
    public static JobExecution create(
            JobType jobType,
            LocalDate executionDate,
            Instant executionStartTime
    ) {
        return new JobExecution(
                jobType,
                executionDate,
                executionStartTime,
                null,
                JobExecutionStatus.RUNNING,
                null
        );
    }

    /**
     * Mark the job execution as completed successfully
     */
    public void markCompleted(Instant executionEndTime) {
        this.executionEndTime = executionEndTime;
        this.status = JobExecutionStatus.COMPLETED;
        this.errorMessage = null;
    }

    /**
     * Mark the job execution as failed
     */
    public void markFailed(Instant executionEndTime, String errorMessage) {
        this.executionEndTime = executionEndTime;
        this.status = JobExecutionStatus.FAILED;
        this.errorMessage = errorMessage;
    }
}
