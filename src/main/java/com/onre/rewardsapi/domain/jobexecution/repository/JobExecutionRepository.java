package com.onre.rewardsapi.domain.jobexecution.repository;

import com.onre.rewardsapi.domain.jobexecution.JobExecution;
import com.onre.rewardsapi.domain.jobexecution.JobExecutionStatus;
import com.onre.rewardsapi.domain.jobexecution.JobType;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

@Repository
public interface JobExecutionRepository extends JpaRepository<JobExecution, UUID> {

    /**
     * Find a job execution by job type and execution date
     */
    Optional<JobExecution> findByJobTypeAndExecutionDate(JobType jobType, LocalDate executionDate);

    /**
     * Check if a job has been completed successfully for a specific date
     */
    boolean existsByJobTypeAndExecutionDateAndStatus(JobType jobType, LocalDate executionDate, JobExecutionStatus status);

    /**
     * Find all job executions for a specific date
     */
    List<JobExecution> findByExecutionDateOrderByCreatedAtDesc(LocalDate executionDate);

    /**
     * Find all job executions for a specific job type
     */
    List<JobExecution> findByJobTypeOrderByExecutionDateDesc(JobType jobType);
}
