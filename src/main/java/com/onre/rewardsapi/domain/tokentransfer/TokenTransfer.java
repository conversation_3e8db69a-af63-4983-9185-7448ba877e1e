package com.onre.rewardsapi.domain.tokentransfer;

import com.onre.rewardsapi.domain.CreatableEntity;
import jakarta.persistence.Entity;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.math.BigInteger;
import java.time.Instant;

@Entity
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class TokenTransfer extends CreatableEntity {

    private String fromSolAddress;

    private String toSolAddress;

    private String fromSolTokenAccount;

    private String toSolTokenAccount;

    @Convert(converter = SolAddressWrapperConverter.class)
    private SolAddressWrapper fromSolTokenAccount;

    @Convert(converter = SolAddressWrapperConverter.class)
    private SolAddressWrapper toSolTokenAccount;

    private String transactionSignature;

    private BigInteger amount;

    private Instant timestamp;
}
