package com.onre.rewardsapi.infrastructure.jobs.properties;

import com.onre.rewardsapi.domain.jobexecution.JobType;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.validation.annotation.Validated;

import java.util.ArrayList;
import java.util.List;

/**
 * Configuration properties for Quartz jobs.
 */
@Data
@Validated
@ConfigurationProperties(prefix = "quartz")
public class QuartzProperties {

    /**
     * Whether Quartz is enabled
     */
    @NotNull
    private Boolean enabled = false;

    /**
     * List of job configurations
     */
    @NotNull
    private List<JobProperties> jobs = new ArrayList<>();

    /**
     * Get job properties for a specific job type.
     * Returns a default disabled configuration if not found.
     *
     * @param jobType the job type
     * @return the job properties
     */
    public JobProperties getJobProperties(JobType jobType) {
        return jobs.stream()
                .filter(job -> job.jobType().equals(jobType))
                .findFirst()
                .orElseThrow(() -> new IllegalStateException("No job properties found for " + jobType));
    }

    /**
     * Properties for individual jobs.
     */
    public record JobProperties(
            @NotNull JobType jobType,
            @NotNull Boolean enabled,
            @NotNull String cron
    ) {
    }
}
