package com.onre.rewardsapi.adapter.in.quartz.service;

import com.onre.rewardsapi.application.module.jobexecution.finder.JobExecutionFinderService;
import com.onre.rewardsapi.application.module.jobexecution.service.JobExecutionRecordingService;
import com.onre.rewardsapi.domain.jobexecution.JobType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.Clock;
import java.time.Instant;
import java.time.LocalDate;
import java.util.Map;

/**
 * Service for tracking job executions and managing job dependencies.
 */
@Slf4j
@Service
public class JobExecutionTrackingService {

    private final JobExecutionRecordingService jobExecutionRecordingService;
    private final JobExecutionFinderService jobExecutionFinderService;
    private final Clock clock;

    // Define job dependencies: job -> list of jobs it depends on
    private static final Map<JobType, JobType[]> JOB_DEPENDENCIES = Map.of(
            JobType.PROCESS_DAILY_TOKEN_BALANCE,
            new JobType[]{JobType.SOLSCAN_TOKEN_TRANSFER}
    );

    public JobExecutionTrackingService(
            JobExecutionRecordingService jobExecutionRecordingService,
            JobExecutionFinderService jobExecutionFinderService,
            Clock clock
    ) {
        this.jobExecutionRecordingService = jobExecutionRecordingService;
        this.jobExecutionFinderService = jobExecutionFinderService;
        this.clock = clock;
    }

    /**
     * Check if all dependencies for a job have been satisfied for today
     */
    public boolean areDependenciesSatisfied(JobType jobType) {
        LocalDate today = LocalDate.now(clock);

        JobType[] dependencies = JOB_DEPENDENCIES.get(jobType);
        if (dependencies == null) {
            return true; // No dependencies
        }

        for (JobType dependency : dependencies) {
            if (!jobExecutionFinderService.hasJobCompletedSuccessfully(dependency, today)) {
                log.info("Job {} cannot run because dependency {} has not completed successfully for date: {}",
                        jobType.name(), dependency, today);
                return false;
            }
        }

        return true;
    }

    /**
     * Record the start of a job execution
     */
    public JobExecutionContext recordJobStart(JobType jobType) {
        LocalDate executionDate = LocalDate.now(clock);
        Instant executionStartTime = Instant.now(clock);

        log.info("Starting job execution tracking for: {} on date: {}", jobType.name(), executionDate);

        return new JobExecutionContext(jobType, executionDate, executionStartTime);
    }

    /**
     * Record the completion of a job execution
     */
    public void recordJobCompletion(JobExecutionContext context, boolean success, String errorMessage) {
        Instant executionEndTime = Instant.now(clock);

        log.info("Recording job execution completion for: {} on date: {} - Success: {}",
                context.jobType(), context.executionDate(), success);

        jobExecutionRecordingService.recordJobExecution(
                context.jobType(),
                context.executionDate(),
                context.executionStartTime(),
                executionEndTime,
                success,
                errorMessage
        );
    }

    /**
     * Context object to track job execution details
     */
    public record JobExecutionContext(
            JobType jobType,
            LocalDate executionDate,
            Instant executionStartTime
    ) {
    }
}
