package com.onre.rewardsapi.adapter.in.quartz;

import com.onre.rewardsapi.adapter.in.quartz.service.JobExecutionTrackingService;
import com.onre.rewardsapi.application.common.command.Command;
import com.onre.rewardsapi.application.common.command.CommandBus;
import com.onre.rewardsapi.domain.jobexecution.JobType;
import lombok.extern.slf4j.Slf4j;
import org.quartz.Job;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;

/**
 * Base implementation of QuartzJob that provides common functionality.
 */
@Slf4j
public abstract class QuartzJob implements Job {

    @Lazy
    @Autowired
    private CommandBus commandBus;

    @Lazy
    @Autowired
    private JobExecutionTrackingService jobExecutionTrackingService;

    /**
     * Get the job type for this job implementation.
     *
     * @return the JobType enum value
     */
    public abstract JobType getJobType();

    /**
     * Create the command to be executed by this job.
     *
     * @param context the job execution context
     * @return the command to execute
     */
    public abstract Command<?> createCommand(JobExecutionContext context);

    /**
     * Get the job name from the job type.
     *
     * @return the job name
     */
    public final String getJobName() {
        return getJobType().getJobName();
    }

    /**
     * Get the job group from the job type.
     *
     * @return the job group
     */
    public final String getJobGroup() {
        return getJobType().getJobGroup();
    }

    /**
     * Get the job description from the job type.
     *
     * @return the job description
     */
    public final String getDescription() {
        return getJobType().getDescription();
    }

    @Override
    public final void execute(JobExecutionContext context) throws JobExecutionException {
        // Check if dependencies are satisfied
        if (!jobExecutionTrackingService.areDependenciesSatisfied(getJobType())) {
            log.info("Skipping job {} because dependencies are not satisfied", getJobName());
            return;
        }

        // Record job start
        JobExecutionTrackingService.JobExecutionContext executionContext =
                jobExecutionTrackingService.recordJobStart(getJobType());

        withLogging(() -> {
            boolean success = false;
            String errorMessage = null;

            try {
                commandBus.handle(createCommand(context));
                success = true;
            } catch (Exception e) {
                errorMessage = e.getMessage();
                throw new RuntimeException(e);
            } finally {
                // Record job completion
                jobExecutionTrackingService.recordJobCompletion(executionContext, success, errorMessage);
            }
        });
    }

    private void withLogging(Runnable block) throws JobExecutionException {
        log.info("Starting job: {}", getJobName());
        try {
            block.run();
        } catch (Exception e) {
            log.error("Error executing job: {}", getJobName(), e);
            throw new JobExecutionException(e);
        } finally {
            log.info("Finished job: {}", getJobName());
        }
    }
}
