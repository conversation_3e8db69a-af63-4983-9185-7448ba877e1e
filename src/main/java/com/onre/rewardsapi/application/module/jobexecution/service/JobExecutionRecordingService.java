package com.onre.rewardsapi.application.module.jobexecution.service;

import com.onre.rewardsapi.domain.jobexecution.JobExecution;
import com.onre.rewardsapi.domain.jobexecution.JobExecutionCreateService;
import com.onre.rewardsapi.domain.jobexecution.JobType;
import com.onre.rewardsapi.domain.jobexecution.repository.JobExecutionRepository;
import com.onre.rewardsapi.infrastructure.lock.AdvisoryLockService;
import com.onre.rewardsapi.infrastructure.lock.constant.ModuleLockNamespace;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.time.LocalDate;
import java.util.Optional;

/**
 * Service for recording job executions.
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class JobExecutionRecordingService {

    private final JobExecutionCreateService jobExecutionCreateService;
    private final JobExecutionRepository jobExecutionRepository;
    private final AdvisoryLockService advisoryLockService;

    /**
     * Record a job execution
     */
    @Transactional
    public void recordJobExecution(
            JobType jobType,
            LocalDate executionDate,
            Instant executionStartTime,
            Instant executionEndTime,
            boolean success,
            String errorMessage
    ) {
        try {

            advisoryLockService.lock(ModuleLockNamespace.TOKEN_TRANSFER, "lockName");

            // Check if there's already a job execution record for this job type and date
            Optional<JobExecution> existingExecution = jobExecutionRepository
                    .findByJobTypeAndExecutionDate(jobType, executionDate);

            JobExecution jobExecution;
            if (existingExecution.isPresent()) {
                // Update existing record
                jobExecution = existingExecution.get();
            } else {
                // Create new record
                jobExecution = jobExecutionCreateService.create(
                        jobType,
                        executionDate,
                        executionStartTime
                );
            }

            // Mark as completed or failed
            if (success) {
                jobExecution.markCompleted(executionEndTime);
            } else {
                jobExecution.markFailed(executionEndTime, errorMessage);
            }

            jobExecutionRepository.save(jobExecution);
            log.debug("Recorded job execution for {} on {}: {}", jobType, executionDate, success ? "SUCCESS" : "FAILED");
        } catch (Exception e) {
            log.error("Failed to record job execution for {} on {}", jobType, executionDate, e);
        }
    }
}
