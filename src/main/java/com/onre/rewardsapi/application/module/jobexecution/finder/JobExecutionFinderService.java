package com.onre.rewardsapi.application.module.jobexecution.finder;

import com.onre.rewardsapi.domain.jobexecution.JobExecutionStatus;
import com.onre.rewardsapi.domain.jobexecution.JobType;
import com.onre.rewardsapi.domain.jobexecution.repository.JobExecutionRepository;
import org.springframework.stereotype.Service;

import java.time.LocalDate;

/**
 * Finder service for job execution entities.
 */
@Service
public class JobExecutionFinderService {

    private final JobExecutionRepository repository;

    public JobExecutionFinderService(JobExecutionRepository repository) {
        this.repository = repository;
    }

    public boolean hasJobCompletedSuccessfully(JobType jobType, LocalDate executionDate) {
        return repository.existsByJobTypeAndExecutionDateAndStatus(
                jobType,
                executionDate,
                JobExecutionStatus.COMPLETED
        );
    }
}
