<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
    http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">

    <changeSet id="create_job_execution_table" author="Marian-<PERSON>" runOnChange="false">
        <preConditions onSqlOutput="TEST" onFail="CONTINUE">
            <not>
                <tableExists tableName="job_execution"/>
            </not>
        </preConditions>

        <createTable tableName="job_execution">
            <column name="id" type="uuid">
                <constraints primaryKey="true" primaryKeyName="PK_job_execution"/>
            </column>
            <column name="job_type" type="varchar(50)">
                <constraints nullable="false"/>
            </column>
            <column name="execution_date" type="date">
                <constraints nullable="false"/>
            </column>
            <column name="execution_start_time" type="timestamptz">
                <constraints nullable="false"/>
            </column>
            <column name="execution_end_time" type="timestamptz">
                <constraints nullable="true"/>
            </column>
            <column name="status" type="varchar(20)">
                <constraints nullable="false"/>
            </column>
            <column name="error_message" type="text">
                <constraints nullable="true"/>
            </column>
            <column name="version" type="int">
                <constraints nullable="false"/>
            </column>
            <column name="created_at" type="timestamptz">
                <constraints nullable="false"/>
            </column>
        </createTable>

        <!-- Create unique index to prevent duplicate job executions for the same date -->
        <createIndex tableName="job_execution" indexName="775fdc295ea745bb9477_ux" unique="true">
            <column name="job_type"/>
            <column name="execution_date"/>
        </createIndex>

        <!-- Create index for faster lookups by job type -->
        <createIndex tableName="job_execution" indexName="9695881B18CE4BE4AAA8_ix">
            <column name="job_type"/>
        </createIndex>

        <!-- Create index for faster lookups by execution date -->
        <createIndex tableName="job_execution" indexName="A1B2C3D4E5F6789012AB_ix">
            <column name="execution_date"/>
        </createIndex>

        <!-- Create index for status lookups -->
        <createIndex tableName="job_execution" indexName="B2C3D4E5F6789012ABC1_ix">
            <column name="status"/>
        </createIndex>
    </changeSet>

</databaseChangeLog>
