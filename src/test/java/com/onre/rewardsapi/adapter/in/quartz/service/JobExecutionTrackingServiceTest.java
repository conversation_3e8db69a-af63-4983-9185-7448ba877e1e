package com.onre.rewardsapi.adapter.in.quartz.service;

import com.onre.rewardsapi.application.module.jobexecution.finder.JobExecutionFinderService;
import com.onre.rewardsapi.application.module.jobexecution.service.JobExecutionRecordingService;
import com.onre.rewardsapi.domain.jobexecution.JobType;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.Clock;
import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneOffset;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import static org.mockito.Mockito.lenient;

@ExtendWith(MockitoExtension.class)
class JobExecutionTrackingServiceTest {

    @Mock
    private JobExecutionRecordingService jobExecutionRecordingService;

    @Mock
    private JobExecutionFinderService jobExecutionFinderService;

    @Mock
    private Clock clock;

    private JobExecutionTrackingService jobExecutionTrackingService;

    private final LocalDate testDate = LocalDate.of(2023, 12, 25);
    private final Instant testInstant = testDate.atStartOfDay(ZoneOffset.UTC).toInstant();

    @BeforeEach
    void setUp() {
        lenient().when(clock.instant()).thenReturn(testInstant);
        lenient().when(clock.getZone()).thenReturn(ZoneOffset.UTC);

        jobExecutionTrackingService = new JobExecutionTrackingService(
                jobExecutionRecordingService,
                jobExecutionFinderService,
                clock
        );
    }

    @Test
    void areDependenciesSatisfied_shouldReturnTrue_whenJobHasNoDependencies() {
        // Given
        JobType jobType = JobType.SOLSCAN_TOKEN_TRANSFER;

        // When
        boolean result = jobExecutionTrackingService.areDependenciesSatisfied(jobType);

        // Then
        assertTrue(result);
        verifyNoInteractions(jobExecutionFinderService);
    }

    @Test
    void areDependenciesSatisfied_shouldReturnTrue_whenAllDependenciesAreSatisfied() {
        // Given
        JobType jobType = JobType.PROCESS_DAILY_TOKEN_BALANCE;

        when(jobExecutionFinderService.hasJobCompletedSuccessfully(
                JobType.SOLSCAN_TOKEN_TRANSFER, testDate))
                .thenReturn(true);

        // When
        boolean result = jobExecutionTrackingService.areDependenciesSatisfied(jobType);

        // Then
        assertTrue(result);
        verify(jobExecutionFinderService).hasJobCompletedSuccessfully(JobType.SOLSCAN_TOKEN_TRANSFER, testDate);
    }

    @Test
    void areDependenciesSatisfied_shouldReturnFalse_whenDependencyIsNotSatisfied() {
        // Given
        JobType jobType = JobType.PROCESS_DAILY_TOKEN_BALANCE;

        when(jobExecutionFinderService.hasJobCompletedSuccessfully(
                JobType.SOLSCAN_TOKEN_TRANSFER, testDate))
                .thenReturn(false);

        // When
        boolean result = jobExecutionTrackingService.areDependenciesSatisfied(jobType);

        // Then
        assertFalse(result);
        verify(jobExecutionFinderService).hasJobCompletedSuccessfully(JobType.SOLSCAN_TOKEN_TRANSFER, testDate);
    }

    @Test
    void recordJobStart_shouldReturnValidContext() {
        // Given
        JobType jobType = JobType.SOLSCAN_TOKEN_TRANSFER;

        // When
        JobExecutionTrackingService.JobExecutionContext context =
                jobExecutionTrackingService.recordJobStart(jobType);

        // Then
        assertNotNull(context);
        assertEquals(JobType.SOLSCAN_TOKEN_TRANSFER, context.jobType());
        assertEquals(testDate, context.executionDate());
        assertEquals(testInstant, context.executionStartTime());
    }

    @Test
    void recordJobCompletion_shouldCallRecordingService() {
        // Given
        JobExecutionTrackingService.JobExecutionContext context =
                new JobExecutionTrackingService.JobExecutionContext(
                        JobType.SOLSCAN_TOKEN_TRANSFER,
                        testDate,
                        testInstant
                );

        boolean success = true;
        String errorMessage = null;

        // When
        jobExecutionTrackingService.recordJobCompletion(context, success, errorMessage);

        // Then
        verify(jobExecutionRecordingService).recordJobExecution(
                eq(JobType.SOLSCAN_TOKEN_TRANSFER),
                eq(testDate),
                eq(testInstant),
                any(Instant.class),
                eq(success),
                eq(errorMessage)
        );
    }
}
